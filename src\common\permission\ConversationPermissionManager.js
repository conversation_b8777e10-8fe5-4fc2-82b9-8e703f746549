import BasePermissionManager from './BasePermissionManager.js';

/**
 * 会话权限管理器
 * 负责聊天会话内的角色权限控制（群主、管理员、普通成员等）
 */
class ConversationPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.conversationRoles = new Map(); // 会话角色缓存 {conversationId: {userId: role}}
        this.conversationPermissions = new Map(); // 会话权限配置
        this.loadConversationPermissions();
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadConversationPermissions();
    }

    /**
     * 加载会话权限配置
     */
    loadConversationPermissions() {
        // 定义会话内的权限配置
        const conversationPermissions = {
            // 消息相关权限
            'message': {
                'send': { roles: ['owner', 'admin', 'member'] },
                'delete': { roles: ['owner', 'admin'], conditions: ['own_message'] },
                'withdraw': { roles: ['owner', 'admin', 'member'], conditions: ['own_message', 'time_limit'] },
                'pin': { roles: ['owner', 'admin'] },
                'forward': { roles: ['owner', 'admin', 'member'] }
            },
            // 成员管理权限
            'member': {
                'invite': { roles: ['owner', 'admin'] },
                'remove': { roles: ['owner', 'admin'] },
                'mute': { roles: ['owner', 'admin'] },
                'set_admin': { roles: ['owner'] },
                'view_list': { roles: ['owner', 'admin', 'member'] }
            },
            // 会话设置权限
            'conversation': {
                'edit_subject': { roles: ['owner', 'admin'] },
                'edit_announcement': { roles: ['owner', 'admin'] },
                'edit_settings': { roles: ['owner', 'admin'] },
                'delete': { roles: ['owner'] },
                'transfer_ownership': { roles: ['owner'] }
            },
            // 文件和资源权限
            'resource': {
                'upload': { roles: ['owner', 'admin', 'member'] },
                'download': { roles: ['owner', 'admin', 'member'] },
                'delete': { roles: ['owner', 'admin'], conditions: ['own_resource'] },
                'share': { roles: ['owner', 'admin', 'member'] }
            },
            // 直播会议权限
            'conference': {
                'start': { roles: ['owner', 'admin'] },
                'end': { roles: ['owner', 'admin'] },
                'mute_all': { roles: ['owner', 'admin'] },
                'control_camera': { roles: ['owner', 'admin'] },
                'screen_share': { roles: ['owner', 'admin', 'member'] }
            }
        };

        this.conversationPermissions.set('default', conversationPermissions);
    }

    /**
     * 设置用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {string} role - 角色 (owner|admin|member|muted)
     */
    setUserConversationRole(conversationId, userId, role) {
        if (!this.conversationRoles.has(conversationId)) {
            this.conversationRoles.set(conversationId, new Map());
        }
        this.conversationRoles.get(conversationId).set(userId, role);

        // 清除相关缓存
        this.clearConversationCache(conversationId);
    }

    /**
     * 获取用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {string} 角色
     */
    getUserConversationRole(conversationId, userId) {
        const conversationRoles = this.conversationRoles.get(conversationId);
        if (!conversationRoles) {
            return 'member'; // 默认为普通成员
        }
        return conversationRoles.get(userId) || 'member';
    }

    /**
     * 批量设置会话成员角色
     * @param {string} conversationId - 会话ID
     * @param {Object} memberRoles - 成员角色映射 {userId: role}
     */
    setConversationMemberRoles(conversationId, memberRoles) {
        if (!this.conversationRoles.has(conversationId)) {
            this.conversationRoles.set(conversationId, new Map());
        }

        const conversationRoleMap = this.conversationRoles.get(conversationId);
        Object.entries(memberRoles).forEach(([userId, role]) => {
            conversationRoleMap.set(userId, role);
        });

        // 清除相关缓存
        this.clearConversationCache(conversationId);
    }

    /**
     * 检查会话权限
     * @param {string} conversationId - 会话ID
     * @param {string} category - 权限类别
     * @param {string} action - 操作
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(conversationId, category, action, context = {}) {
        if (!this.isInitialized()) {
            console.warn('ConversationPermissionManager not initialized');
            return false;
        }

        const userId = context.userId || this.getUserId();
        if (!userId) {
            return false;
        }

        // 获取用户在会话中的角色
        const userRole = this.getUserConversationRole(conversationId, userId);

        // 被禁言的用户没有任何权限
        if (userRole === 'muted') {
            return false;
        }

        // 获取权限配置
        const permissionConfig = this.getConversationPermissionConfig(category, action);
        if (!permissionConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        // 检查角色权限
        if (!permissionConfig.roles.includes(userRole)) {
            return false;
        }

        // 检查额外条件
        if (permissionConfig.conditions) {
            return this.checkConditions(permissionConfig.conditions, context);
        }

        return true;
    }

    /**
     * 获取会话权限配置
     * @param {string} category - 权限类别
     * @param {string} action - 操作
     * @returns {Object|null} 权限配置
     */
    getConversationPermissionConfig(category, action) {
        const permissions = this.conversationPermissions.get('default');
        if (!permissions || !permissions[category]) {
            return null;
        }
        return permissions[category][action] || null;
    }

    /**
     * 检查额外条件
     * @param {Array<string>} conditions - 条件列表
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否满足条件
     */
    checkConditions(conditions, context) {
        return conditions.every(condition => {
            switch (condition) {
                case 'own_message':
                    return context.messageOwnerId === context.userId;
                case 'own_resource':
                    return context.resourceOwnerId === context.userId;
                case 'time_limit':
                    // 检查时间限制（例如：消息发送后5分钟内可以撤回）
                    const timeLimit = 5 * 60 * 1000; // 5分钟
                    const messageTime = context.messageTime || Date.now();
                    return (Date.now() - messageTime) <= timeLimit;
                default:
                    return true;
            }
        });
    }

    /**
     * 检查是否为会话所有者
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为所有者
     */
    isConversationOwner(conversationId, userId = null) {
        const targetUserId = userId || this.getUserId();
        return this.getUserConversationRole(conversationId, targetUserId) === 'owner';
    }

    /**
     * 检查是否为会话管理员（包括所有者）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为管理员
     */
    isConversationAdmin(conversationId, userId = null) {
        const targetUserId = userId || this.getUserId();
        const role = this.getUserConversationRole(conversationId, targetUserId);
        return role === 'owner' || role === 'admin';
    }

    /**
     * 获取用户在会话中的权限列表
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {Array<string>} 权限列表
     */
    getUserConversationPermissions(conversationId, userId = null) {
        const targetUserId = userId || this.getUserId();
        const permissions = [];
        const allPermissions = this.conversationPermissions.get('default');

        if (!allPermissions) {
            return permissions;
        }

        Object.keys(allPermissions).forEach(category => {
            Object.keys(allPermissions[category]).forEach(action => {
                if (this.hasPermission(conversationId, category, action, { userId: targetUserId })) {
                    permissions.push(`${category}.${action}`);
                }
            });
        });

        return permissions;
    }

    /**
     * 清除会话相关缓存
     * @param {string} conversationId - 会话ID
     */
    clearConversationCache(conversationId) {
        // 清除与特定会话相关的缓存
        const keysToDelete = [];
        for (const key of this.permissions.keys()) {
            if (key.includes(conversationId)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.permissions.delete(key));
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        this.conversationRoles.clear();
        this.conversationPermissions.clear();
        super.destroy();
    }
}

export default ConversationPermissionManager;
