// 用户角色常量
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
    0: 'TEMP_USER',
    1: 'NORMAL_USER',
    2: 'ADMIN',
    3: 'SUPER_ADMIN',
    4: 'DIRECTOR'
});

// 权限类型常量
export const PERMISSION_TYPE = Object.freeze({
    ROUTE: 'route',           // 路由权限
    COMPONENT: 'component',   // 组件权限
    FEATURE: 'feature',       // 功能权限
    API: 'api',              // API权限
    DATA: 'data',            // 数据权限
    REGION: 'region',        // 区域权限
    CONVERSATION: 'conversation' // 会话权限
});

// 权限操作常量
export const PERMISSION_ACTION = Object.freeze({
    VIEW: 'view',         // 查看
    CREATE: 'create',     // 创建
    UPDATE: 'update',     // 更新
    DELETE: 'delete',     // 删除
    MANAGE: 'manage',     // 管理
    EXECUTE: 'execute'    // 执行
});

// 权限状态常量
export const PERMISSION_STATUS = Object.freeze({
    GRANTED: 'granted',   // 已授权
    DENIED: 'denied',     // 已拒绝
    PENDING: 'pending'    // 待审核
});
