import BasePermissionManager from './BasePermissionManager.js';
import { USER_ROLE } from './constant.js';

/**
 * 功能权限管理器
 * 负责具体功能操作的权限控制
 */
class FeaturePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.featurePermissions = new Map(); // 功能权限配置
        this.apiPermissions = new Map(); // API权限配置
        this.dataPermissions = new Map(); // 数据权限配置
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadFeaturePermissions();
        this.loadApiPermissions();
        this.loadDataPermissions();
    }

    /**
     * 加载功能权限配置
     */
    loadFeaturePermissions() {
        const featurePermissions = {
            // 后台管理功能
            'backgroundManage': {
                'default': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: [] },
            },
            // 电视墙功能
            'tvWall': {
                'default': { roles: [USER_ROLE.DIRECTOR, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['tvWall'] },
            },
            // 用户管理功能
            'userManage': {
                'view': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_view'] },
                'create': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_create'] },
                'edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_edit'] },
                'delete': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['user_delete'] }
            },
            // 群组管理功能
            'groupManage': {
                'view': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['group_view'] },
                'create': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_create'] },
                'edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_edit'] },
                'delete': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['group_delete'] }
            }
        };

        Object.entries(featurePermissions).forEach(([feature, permissions]) => {
            this.featurePermissions.set(feature, permissions);
        });
    }

    /**
     * 加载API权限配置
     */
    loadApiPermissions() {
        const apiPermissions = {
            // 用户相关API
            'POST /api/user/create': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_create'] },
            'PUT /api/user/update': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_edit'] },
            'DELETE /api/user/delete': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['user_delete'] },
            'GET /api/user/list': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['user_view'] },
            'POST /api/user/role': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['user_role_change'] },

            // 群组相关API
            'POST /api/group/create': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_create'] },
            'PUT /api/group/update': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_edit'] },
            'DELETE /api/group/delete': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['group_delete'] },
            'POST /api/group/members': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_member_manage'] },

            // 多中心相关API
            'POST /api/multicenter/exam': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['exam_create'] },
            'PUT /api/multicenter/exam': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['exam_edit'] },
            'DELETE /api/multicenter/exam': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['exam_delete'] },
            'POST /api/multicenter/assign': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['exam_assign'] },
            'POST /api/multicenter/review': { roles: [USER_ROLE.ADMIN, USER_ROLE.DIRECTOR], permissions: ['exam_review'] },

            // 文件相关API
            'POST /api/file/upload': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['file_upload'] },
            'GET /api/file/download': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['file_download'] },
            'DELETE /api/file/delete': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['file_delete'] },
            'POST /api/data/export': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['data_export'] }
        };

        Object.entries(apiPermissions).forEach(([api, config]) => {
            this.apiPermissions.set(api, config);
        });
    }

    /**
     * 加载数据权限配置
     */
    loadDataPermissions() {
        const dataPermissions = {
            // 用户数据权限
            'user_data': {
                'own_data': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['own_data_access'] },
                'department_data': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['department_data_access'] },
                'all_data': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['all_data_access'] }
            },

            // 群组数据权限
            'group_data': {
                'member_data': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['group_member_data'] },
                'manager_data': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_manager_data'] },
                'admin_data': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['group_admin_data'] }
            },

            // 多中心数据权限
            'multicenter_data': {
                'own_exam': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['own_exam_data'] },
                'department_exam': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['department_exam_data'] },
                'all_exam': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['all_exam_data'] }
            }
        };

        Object.entries(dataPermissions).forEach(([dataType, permissions]) => {
            this.dataPermissions.set(dataType, permissions);
        });
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(feature, action = 'default', context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        const featureConfig = this.featurePermissions.get(feature);
        if (!featureConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        const actionConfig = featureConfig[action] || featureConfig.default;
        if (!actionConfig) {
            // 如果没有对应的action配置，默认允许
            return true;
        }

        return this.checkPermissionConfig(actionConfig, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        const apiKey = `${method.toUpperCase()} ${path}`;
        const apiConfig = this.apiPermissions.get(apiKey);

        if (!apiConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        return this.checkPermissionConfig(apiConfig, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope = 'own_data', context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        const dataConfig = this.dataPermissions.get(dataType);
        if (!dataConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        const scopeConfig = dataConfig[scope];
        if (!scopeConfig) {
            // 如果没有对应的scope配置，默认允许
            return true;
        }

        return this.checkPermissionConfig(scopeConfig, context);
    }

    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {
        const userRole = this.getUserRole();

        // 检查角色权限
        let hasRolePermission = false;
        if (config.roles && config.roles.length > 0) {
            hasRolePermission = config.roles.includes(userRole);
        } else {
            hasRolePermission = true; // 没有角色限制
        }

        // 检查特定权限
        let hasSpecificPermission = false;
        if (config.permissions && config.permissions.length > 0) {
            hasSpecificPermission = config.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        } else {
            hasSpecificPermission = true; // 没有特定权限限制
        }

        // 默认使用OR逻辑
        const strategy = config.strategy || 'OR';
        if (strategy === 'AND') {
            return hasRolePermission && hasSpecificPermission;
        } else {
            return hasRolePermission || hasSpecificPermission;
        }
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        // 这里可以实现具体的权限检查逻辑
        // 例如检查用户是否有特定的功能权限
        return true; // 默认返回true，具体实现可以根据业务需求调整
    }

    /**
     * 获取可用的功能操作列表
     * @param {string} feature - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {Array<string>} 可用的操作列表
     */
    getAvailableActions(feature, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);
        if (!featureConfig) {
            return [];
        }

        const availableActions = [];
        Object.keys(featureConfig).forEach(action => {
            if (this.hasPermission(feature, action, context)) {
                availableActions.push(action);
            }
        });

        return availableActions;
    }
}

export default FeaturePermissionManager;
