import BasePermissionManager from './BasePermissionManager.js';
import { USER_ROLE } from './constant.js';

/**
 * 组件权限管理器
 * 负责组件级别的权限控制和显示隐藏逻辑
 */
class ComponentPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.componentPermissions = new Map(); // 组件权限配置
        this.elementPermissions = new Map(); // 元素权限配置
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadComponentPermissions();
        this.loadElementPermissions();
    }

    /**
     * 加载组件权限配置
     */
    loadComponentPermissions() {
        const componentPermissions = {
            // 菜单组件权限
            'menu': {
                'system_setting': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['user'] },
                'system_info': { roles: [USER_ROLE.NORMAL_USER, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['user'] },
                'background_manage': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['admin'] }
            },

            // 侧边栏组件权限
            'sidebar': {
                'admin_panel': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['admin'] },
                'user_management': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_manage'] },
                'group_management': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_manage'] }
            },

            // 多中心组件权限
            'multicenter': {
                'admin_view': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['super_admin'] },
                'judge_view': { roles: [USER_ROLE.DIRECTOR], permissions: ['judge'] },
                'annotation_view': { roles: [USER_ROLE.ADMIN], permissions: ['review'] },
                'assignment_view': { roles: [USER_ROLE.ADMIN], permissions: ['assignment'] },
                'normal_view': { roles: [USER_ROLE.NORMAL_USER], permissions: ['normal'] }
            },

            // 按钮组件权限
            'button': {
                'delete': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['delete'] },
                'edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['edit'] },
                'create': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['create'] },
                'export': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['export'] },
                'import': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['import'] }
            },

            // 表格组件权限
            'table': {
                'user_role_edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_role_edit'] },
                'data_export': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['data_export'] },
                'batch_operation': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['batch_operation'] }
            },

            // 对话框组件权限
            'dialog': {
                'user_edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_edit'] },
                'group_edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['group_edit'] },
                'organization_edit': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['organization_edit'] }
            }
        };

        // 将配置存储到Map中
        Object.entries(componentPermissions).forEach(([component, permissions]) => {
            this.componentPermissions.set(component, permissions);
        });
    }

    /**
     * 加载元素权限配置
     */
    loadElementPermissions() {
        const elementPermissions = {
            // 基于ID的元素权限
            'admin-panel': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['admin'] },
            'user-management-btn': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['user_manage'] },
            'delete-btn': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['delete'] },
            'edit-btn': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['edit'] },

            // 基于class的元素权限
            '.admin-only': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['admin'] },
            '.super-admin-only': { roles: [USER_ROLE.SUPER_ADMIN], permissions: ['super_admin'] },
            '.manager-only': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN, USER_ROLE.DIRECTOR], permissions: ['manager'] }
        };

        Object.entries(elementPermissions).forEach(([element, config]) => {
            this.elementPermissions.set(element, config);
        });
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(component, action = null, context = {}) {
        if (!this.isInitialized()) {
            console.warn('ComponentPermissionManager not initialized');
            return false;
        }

        // 获取组件权限配置
        const componentConfig = this.componentPermissions.get(component);
        if (!componentConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        // 如果指定了action，检查具体action的权限
        if (action && componentConfig[action]) {
            return this.checkPermissionConfig(componentConfig[action], context);
        }

        // 如果没有指定action，检查组件的默认权限
        if (componentConfig.default) {
            return this.checkPermissionConfig(componentConfig.default, context);
        }

        // 如果没有默认权限配置，默认允许
        return true;
    }

    /**
     * 检查元素权限
     * @param {string} element - 元素选择器
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkElementPermission(element, context = {}) {
        if (!this.isInitialized()) {
            console.warn('ComponentPermissionManager not initialized');
            return false;
        }

        const elementConfig = this.elementPermissions.get(element);
        if (!elementConfig) {
            // 如果没有配置，默认允许
            return true;
        }

        return this.checkPermissionConfig(elementConfig, context);
    }

    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {
        const userRole = this.getUserRole();

        // 检查角色权限
        let hasRolePermission = false;
        if (config.roles && config.roles.length > 0) {
            hasRolePermission = config.roles.includes(userRole);
        } else {
            hasRolePermission = true; // 没有角色限制
        }

        // 检查特定权限
        let hasSpecificPermission = false;
        if (config.permissions && config.permissions.length > 0) {
            hasSpecificPermission = config.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        } else {
            hasSpecificPermission = true; // 没有特定权限限制
        }

        // 默认使用OR逻辑
        const strategy = config.strategy || 'OR';
        if (strategy === 'AND') {
            return hasRolePermission && hasSpecificPermission;
        } else {
            return hasRolePermission || hasSpecificPermission;
        }
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        // 这里可以实现具体的权限检查逻辑
        // 例如检查用户是否有特定的功能权限
        return true; // 默认返回true，具体实现可以根据业务需求调整
    }

    /**
     * 检查组件是否可见
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否可见
     */
    isComponentVisible(component, action = null, context = {}) {
        return this.hasPermission(component, action, context);
    }

    /**
     * 检查组件是否禁用
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否禁用
     */
    isComponentDisabled(component, action = null, context = {}) {
        return !this.hasPermission(component, action, context);
    }

    /**
     * 获取可用的操作列表
     * @param {string} component - 组件名称
     * @param {Object} context - 上下文信息
     * @returns {Array<string>} 可用的操作列表
     */
    getAvailableActions(component, context = {}) {
        const componentConfig = this.componentPermissions.get(component);
        if (!componentConfig) {
            return [];
        }

        const availableActions = [];
        Object.keys(componentConfig).forEach(action => {
            if (action !== 'default' && this.hasPermission(component, action, context)) {
                availableActions.push(action);
            }
        });

        return availableActions;
    }
}

export default ComponentPermissionManager;
