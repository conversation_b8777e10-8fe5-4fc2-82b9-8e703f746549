import { USER_ROLE } from './constant.js';
import BasePermissionManager from './BasePermissionManager.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';
import RegionPermissionManager from './RegionPermissionManager.js';
import ConversationPermissionManager from './ConversationPermissionManager.js';

/**
 * 主权限管理器单例
 * 统一管理所有子权限管理器，提供统一的权限检查接口
 */
class PermissionManager extends BasePermissionManager {
    constructor() {
        if (PermissionManager.instance) {
            return PermissionManager.instance;
        }

        // 调用父类构造函数
        super();

        // 延迟初始化其他权限管理器，只在构造函数中初始化区域权限管理器
        this.routeManager = null;
        this.componentManager = null;
        this.featureManager = null;
        this.regionManager = new RegionPermissionManager();
        this.conversationManager = null;

        // 区域权限初始化状态（应用初始化时的权限）
        this.regionInitialized = false;

        PermissionManager.instance = this;
    }

    /**
     * 获取单例实例
     * @returns {PermissionManager} 权限管理器实例
     */
    static getInstance() {
        if (!PermissionManager.instance) {
            PermissionManager.instance = new PermissionManager();
        }
        return PermissionManager.instance;
    }

    /**
     * 初始化区域权限管理器（在应用初始化时调用）
     * @param {Object} config - 配置信息
     */
    async initializeRegionPermissions(config = {}) {
        try {
            if (this.regionInitialized) {
                console.log('RegionPermissionManager already initialized');
                return;
            }

            this.config = { ...this.config, ...config };

            // 只初始化区域权限管理器
            await this.regionManager.initialize(null, this.config);
            this.regionInitialized = true;

            // 触发区域权限初始化完成事件
            if (window.vm && window.vm.$root && window.vm.$root.eventBus) {
                window.vm.$root.eventBus.$emit('permission:regionInitialized', { config: this.config });
            }

            console.log('RegionPermissionManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize RegionPermissionManager:', error);
            throw error;
        }
    }

    /**
     * 初始化用户权限管理器（在用户登录后调用）
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        try {
            // 调用父类的初始化方法
            await super.initialize(userInfo, config);

            // 延迟创建其他权限管理器
            if (!this.routeManager) {
                this.routeManager = new RoutePermissionManager();
                this.componentManager = new ComponentPermissionManager();
                this.featureManager = new FeaturePermissionManager();
                this.conversationManager = new ConversationPermissionManager();

                // 设置路由管理器为全局实例
                RoutePermissionManager.setGlobalInstance(this.routeManager);
            }

            // 初始化用户相关的权限管理器
            await Promise.all([
                this.routeManager.initialize(userInfo, this.config),
                this.componentManager.initialize(userInfo, this.config),
                this.featureManager.initialize(userInfo, this.config),
                this.conversationManager.initialize(userInfo, this.config)
            ]);

            // 触发初始化完成事件
            this.emitEvent('initialized', { userInfo, config: this.config });

            console.log('PermissionManager initialized successfully', userInfo);
        } catch (error) {
            console.error('Failed to initialize PermissionManager:', error);
            throw error;
        }
    }

    /**
     * 加载权限数据（实现基类的抽象方法）
     * PermissionManager 本身不直接加载权限，而是通过子管理器加载
     */
    async loadPermissions() {
        // PermissionManager 本身不需要加载权限数据
        // 权限数据由各个子管理器负责加载
        console.log('PermissionManager loadPermissions called');
    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        if (!this.initialized || !this.routeManager) {
            console.warn('RoutePermissionManager not initialized, returning false for route:', routePath);
            return false;
        }
        return this.routeManager.hasPermission(routePath, context);
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentPermission(component, action = null, context = {}) {
        if (!this.initialized || !this.componentManager) {
            console.warn('ComponentPermissionManager not initialized, returning false for component:', component);
            return false;
        }
        return this.componentManager.hasPermission(component, action, context);
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeaturePermission(feature, action = 'default', context = {}) {
        if (!this.initialized || !this.featureManager) {
            console.warn('FeaturePermissionManager not initialized, returning false for feature:', feature);
            return false;
        }
        return this.featureManager.hasPermission(feature, action, context);
    }

    /**
     * 检查区域权限
     * @param {string} functionName - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRegionPermission(functionName, context = {}) {
        if (!this.regionInitialized || !this.regionManager) {
            console.warn('RegionPermissionManager not initialized, returning false for function:', functionName);
            return false;
        }
        return this.regionManager.hasPermission(functionName, context);
    }

    /**
     * 检查会话权限
     * @param {string} conversationId - 会话ID
     * @param {string} category - 权限类别
     * @param {string} action - 操作
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkConversationPermission(conversationId, category, action, context = {}) {
        if (!this.initialized || !this.conversationManager) {
            console.warn('ConversationPermissionManager not initialized, returning false for conversation:', conversationId);
            return false;
        }
        return this.conversationManager.hasPermission(conversationId, category, action, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        if (!this.initialized || !this.featureManager) {
            console.warn('FeaturePermissionManager not initialized, returning false for API:', `${method} ${path}`);
            return false;
        }
        return this.featureManager.checkApiPermission(method, path, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope = 'own_data', context = {}) {
        if (!this.initialized || !this.featureManager) {
            console.warn('FeaturePermissionManager not initialized, returning false for data:', dataType);
            return false;
        }
        return this.featureManager.checkDataPermission(dataType, scope, context);
    }

    /**
     * 检查元素权限
     * @param {string} element - 元素选择器
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkElementPermission(element, context = {}) {
        if (!this.initialized || !this.componentManager) {
            console.warn('ComponentPermissionManager not initialized, returning false for element:', element);
            return false;
        }
        return this.componentManager.checkElementPermission(element, context);
    }

    /**
     * 通用权限检查方法
     * @param {string|Object} permission - 权限标识或权限对象
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermission(permission, context = {}) {
        if (typeof permission === 'string') {
            // 简单字符串权限检查，默认使用功能权限
            return this.checkFeaturePermission(permission, 'default', context);
        }

        if (typeof permission === 'object') {
            const { type, regionPermissionKey, featurePermissionKey } = permission;

            // 区域权限和功能权限组合检查
            if (regionPermissionKey && featurePermissionKey) {
                const hasRegionPermission = this.checkRegionPermission(regionPermissionKey, context);
                const hasFeaturePermission = this.checkFeaturePermission(featurePermissionKey, 'default', context);
                return hasRegionPermission && hasFeaturePermission;
            }

            // 根据类型进行权限检查
            switch (type) {
            case 'route':
                return this.checkRoutePermission(permission.permission, context);
            case 'component':
                return this.checkComponentPermission(permission.permission, permission.action, context);
            case 'feature':
                return this.checkFeaturePermission(permission.permission, permission.action, context);
            case 'api':
                return this.checkApiPermission(permission.method, permission.path, context);
            case 'data':
                return this.checkDataPermission(permission.dataType, permission.scope, context);
            case 'region':
                return this.checkRegionPermission(permission.permission, context);
            case 'conversation':
                return this.checkConversationPermission(permission.conversationId, permission.category, permission.action, context);
            default:
                return this.checkFeaturePermission(permission.permission, permission.action, context);
            }
        }

        return false;
    }

    /**
     * 检查组件是否可见
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否可见
     */
    isComponentVisible(component, action = null, context = {}) {
        return this.checkComponentPermission(component, action, context);
    }

    /**
     * 检查组件是否禁用
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否禁用
     */
    isComponentDisabled(component, action = null, context = {}) {
        return !this.checkComponentPermission(component, action, context);
    }

    /**
     * 获取可访问的路由列表
     * @returns {Array<string>} 可访问的路由列表
     */
    getAccessibleRoutes() {
        if (!this.initialized || !this.routeManager) {
            return [];
        }
        return this.routeManager.getAccessibleRoutes();
    }

    /**
     * 获取可用的操作列表
     * @param {string} component - 组件名称
     * @param {Object} context - 上下文信息
     * @returns {Array<string>} 可用的操作列表
     */
    getAvailableActions(component, context = {}) {
        if (!this.initialized || !this.componentManager) {
            return [];
        }
        return this.componentManager.getAvailableActions(component, context);
    }

    /**
     * 获取重定向路由
     * @param {string} requestedRoute - 请求的路由
     * @returns {string} 重定向路由
     */
    getRedirectRoute(requestedRoute) {
        if (!this.initialized || !this.routeManager) {
            return '/login';
        }
        return this.routeManager.getRedirectRoute(requestedRoute);
    }

    /**
     * 批量检查权限
     * @param {Array<Object>} permissions - 权限列表
     * @param {Object} context - 上下文信息
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(permissions, context = {}) {
        const results = {};
        permissions.forEach(permission => {
            const key = permission.key || JSON.stringify(permission);
            results[key] = this.checkPermission(permission, context);
        });
        return results;
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        const oldRole = this.getUserRole();
        const oldUserId = this.getUserId();

        // 调用父类方法更新用户信息
        super.updateUserInfo(userInfo);

        // 更新所有子管理器的用户信息
        if (this.routeManager) {
            this.routeManager.updateUserInfo(userInfo);
        }
        if (this.componentManager) {
            this.componentManager.updateUserInfo(userInfo);
        }
        if (this.featureManager) {
            this.featureManager.updateUserInfo(userInfo);
        }
        if (this.conversationManager) {
            this.conversationManager.updateUserInfo(userInfo);
        }

        // 触发权限变化事件
        const newRole = this.getUserRole();
        const newUserId = this.getUserId();

        if (oldRole !== newRole || oldUserId !== newUserId) {
            this.emitPermissionChange({
                oldRole,
                newRole,
                oldUserId,
                newUserId,
                userInfo
            });
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        super.clearCache();
        if (this.routeManager) {
            this.routeManager.clearCache();
        }
        if (this.componentManager) {
            this.componentManager.clearCache();
        }
        if (this.featureManager) {
            this.featureManager.clearCache();
        }
        if (this.regionManager) {
            this.regionManager.clearCache();
        }
        if (this.conversationManager) {
            this.conversationManager.clearCache();
        }
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     */
    emitEvent(eventName, data) {
        if (typeof window !== 'undefined') {
            const event = new CustomEvent(`permission:${eventName}`, {
                detail: data
            });
            window.dispatchEvent(event);
        }
    }

    // 区域权限相关方法代理
    isRegionFunctionEnabled(functionName) {
        return this.regionManager ? this.regionManager.isRegionFunctionEnabled(functionName) : false;
    }

    getEnabledRegionFunctions() {
        return this.regionManager ? this.regionManager.getEnabledRegionFunctions() : [];
    }

    getCurrentRegion() {
        return this.regionManager ? this.regionManager.getCurrentRegion() : '';
    }

    isRegionFunctionAvailable(functionName, options = {}) {
        return this.regionManager ? this.regionManager.isRegionFunctionAvailable(functionName, options) : false;
    }

    getRegionMappedPermissions(regionFunction) {
        return this.regionManager ? this.regionManager.getRegionMappedPermissions(regionFunction) : [];
    }

    checkAllRegionMappedPermissions(regionFunction) {
        return this.regionManager ? this.regionManager.checkAllRegionMappedPermissions(regionFunction) : false;
    }

    getAllEnabledMappedPermissions() {
        return this.regionManager ? this.regionManager.getAllEnabledMappedPermissions() : [];
    }

    getRegionPermissionMappingSummary() {
        return this.regionManager ? this.regionManager.getRegionPermissionMappingSummary() : {};
    }

    // 会话权限相关方法代理
    setUserConversationRole(conversationId, userId, role) {
        if (this.conversationManager) {
            this.conversationManager.setUserConversationRole(conversationId, userId, role);
        }
    }

    getUserConversationRole(conversationId, userId) {
        return this.conversationManager ? this.conversationManager.getUserConversationRole(conversationId, userId) : 'member';
    }

    setConversationMemberRoles(conversationId, memberRoles) {
        if (this.conversationManager) {
            this.conversationManager.setConversationMemberRoles(conversationId, memberRoles);
        }
    }

    isConversationOwner(conversationId, userId = null) {
        return this.conversationManager ? this.conversationManager.isConversationOwner(conversationId, userId) : false;
    }

    isConversationAdmin(conversationId, userId = null) {
        return this.conversationManager ? this.conversationManager.isConversationAdmin(conversationId, userId) : false;
    }

    getUserConversationPermissions(conversationId, userId = null) {
        return this.conversationManager ? this.conversationManager.getUserConversationPermissions(conversationId, userId) : [];
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        // 销毁所有子管理器
        if (this.routeManager) {
            this.routeManager.destroy();
        }
        if (this.componentManager) {
            this.componentManager.destroy();
        }
        if (this.featureManager) {
            this.featureManager.destroy();
        }
        if (this.regionManager) {
            this.regionManager.destroy();
        }
        if (this.conversationManager) {
            this.conversationManager.destroy();
        }

        // 重置实例
        PermissionManager.instance = null;

        super.destroy();
    }
}

// 创建并导出单例实例
const permissionManager = PermissionManager.getInstance();

export default permissionManager;
export { PermissionManager };
