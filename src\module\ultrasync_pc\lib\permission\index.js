/**
 * 权限管理器入口文件
 * 提供统一的权限管理接口
 */

// 从公共权限模块导入所有内容
import permissionManager, {
    PermissionManager,
    BasePermissionManager,
    RoutePermissionManager,
    ComponentPermissionManager,
    FeaturePermissionManager,
    ConversationPermissionManager,
    RegionPermissionManager,
    install,
    permissionMixin
} from '@/common/permission/index.js';

// 重新导出公共权限模块的所有内容，保持向后兼容
export default permissionManager;
export {
    PermissionManager,
    BasePermissionManager,
    RoutePermissionManager,
    ComponentPermissionManager,
    FeaturePermissionManager,
    ConversationPermissionManager,
    RegionPermissionManager,
    install,
    permissionMixin
};

// 便捷方法导出
export const {
    checkPermission,
    checkRoutePermission,
    checkComponentPermission,
    checkFeaturePermission,
    checkRegionPermission,
    checkConversationPermission,
    checkApiPermission,
    checkDataPermission,
    checkElementPermission,
    isComponentVisible,
    isComponentDisabled,
    getAccessibleRoutes,
    getAvailableActions,
    getRedirectRoute,
    batchCheckPermissions,
    updateUserInfo,
    clearCache,
    getUserInfo,
    getUserRole,
    getUserId,
    isAdmin,
    isSuperAdmin,
    isInitialized,
    initialize,
    initializeRegionPermissions,
    destroy,
    isRegionFunctionEnabled,
    getEnabledRegionFunctions,
    getCurrentRegion,
    isRegionFunctionAvailable,
    getRegionMappedPermissions,
    checkAllRegionMappedPermissions,
    getAllEnabledMappedPermissions,
    getRegionPermissionMappingSummary,
    // 会话权限相关方法
    setUserConversationRole,
    getUserConversationRole,
    setConversationMemberRoles,
    isConversationOwner,
    isConversationAdmin,
    getUserConversationPermissions
} = permissionManager;
